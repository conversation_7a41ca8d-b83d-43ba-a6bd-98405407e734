import { Divider, notification } from 'antd';
import type { FC } from 'react';
import { useCallback, useEffect, useMemo } from 'react';
import { useToggle } from 'react-use';

import type { WorkGroupCreationPlanProps } from 'widgets/WorkGroupCreationPlan';
import { WorkGroupCreationPlanStore } from 'widgets/WorkGroupCreationPlan';
import { permissionsConfig, permissionsStore } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import { asyncDownloadFile, downloadFile } from 'shared/lib';
import {
  normalizeFileName,
  useAppDispatch,
  useAppSelector,
  useHandleCloseOpen,
} from 'shared/model';
import { useCreateSliceActions } from 'shared/model/useCreateSliceActions';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { SearchDrawer } from './SearchDrawer';
import styles from './styles.module.scss';
import { WorkGroupTable } from './WorkGroupTable';

export const WorkGroupCreationPlan: FC<WorkGroupCreationPlanProps> = ({
  handleClose,
  title,
  getBadgeCount,
  refetch,
}) => {
  const dispatch = useAppDispatch();
  const [isFullSize, toggleFullSize] = useToggle(false);
  const [isDrawerOpen, openD, closeD] = useHandleCloseOpen();

  /* ----------------------------------------------------
   *                      Экшены
   ---------------------------------------------------- */
  const { handleResetSearch, reset } = useCreateSliceActions(
    WorkGroupCreationPlanStore.reducers.slice.actions,
  );

  /* ----------------------------------------------------
   *                      Селекторы
   ---------------------------------------------------- */

  const plan = useAppSelector(
    WorkGroupCreationPlanStore.selectors.planSelector,
  );

  const permissions = useAppSelector(
    permissionsStore.selectors.permissionsSelector,
  );

  /* ----------------------------------------------------
   *                    Колбеки
   ---------------------------------------------------- */

  const getTableWithFilters = useCallback(() => {
    dispatch(
      WorkGroupCreationPlanStore.actions.getDataThunk({
        url: apiUrls.workGroupControl.creationPlan.getTable,
        // Фильтруем пустые поля
        body: Object.fromEntries(
          Object.entries(plan.searchInputs).filter(([, v]) => v),
        ),
      }),
    )
      .unwrap()
      .then(() => closeD());
  }, [plan.searchInputs]); // eslint-disable-line

  const getTableWithoutFilters = useCallback(() => {
    dispatch(
      WorkGroupCreationPlanStore.actions.getDataThunk({
        url: apiUrls.workGroupControl.creationPlan.getTable,
        body: {},
      }),
    );
  }, []); // eslint-disable-line

  const getTableAndResetFilters = useCallback(() => {
    handleResetSearch();
    getTableWithoutFilters();
  }, [getTableWithoutFilters, handleResetSearch]);

  const downloadReport = useCallback(async () => {
    const res = await appInstance.post(
      apiUrls.workGroupControl.creationPlan.getReport,
      plan.searchInputs,
      {
        responseType: 'blob',
      },
    );
    downloadFile(res.data, normalizeFileName(res.headers));
  }, [plan.searchInputs]);

  /* ----------------------------------------------------
   *                      Мемоизация
   ---------------------------------------------------- */

  const filteredCheckedRows = plan.table.rows.filter(
    (row) => row.rowId?.checkedRows,
  );
  const isRowsChecked = filteredCheckedRows.some((row) => row.rowId?.checked);
  const canEdit = permissions.ap_PlanWGO.includes(
    permissionsStore.enums.Actions.EDIT_LIST_AT,
  );

  const memoTable = useMemo(
    () => (
      <WorkGroupTable
        isFullSize={isFullSize}
        data={plan.table}
        isPending={plan.table.isPending}
        refetch={getTableAndResetFilters}
      />
    ),
    [isFullSize, plan.table], // eslint-disable-line
  );

  const actionButtons = useMemo<AdditionalButton[]>(
    () => [
      {
        title: 'Сохранить',
        key: 'save',
        type: 'primary',
        loading: plan.buttons.isPending,
        disabled:
          !canEdit || plan.table.rows.every((i) => !i.rowId?.checkedRows),
        tooltip: !canEdit
          ? permissionsConfig.warnMessages.noPermissionsDefault(
              'сохранение настроек',
            )
          : undefined,
        onClick: async () => {
          await dispatch(
            WorkGroupCreationPlanStore.actions.updateDataThunk({
              url: apiUrls.workGroupControl.creationPlan.updateTale,
              body: plan.table.rows
                .filter((item) => item.rowId?.checkedRows)
                .map((item) => ({ ...item.rowId })),
            }),
          )
            .unwrap()
            .then(() => {
              notification.success({
                message: 'Данные успешно сохранены',
              });
              getTableWithFilters();
            });
        },
      },
      {
        title: 'Создать кабинет',
        key: 'create',
        loading: plan.buttons.isPending,
        disabled:
          !canEdit || plan.table.rows.every((i) => !i.rowId?.checkedRows),
        tooltip: !canEdit
          ? permissionsConfig.warnMessages.noPermissionsDefault(
              'создание кабинета',
            )
          : isRowsChecked
          ? 'Для создания КРГ в ручном режиме нужно деактивировать расписание'
          : undefined,
        onClick: async () => {
          if (isRowsChecked) {
            notification.warning({
              message:
                'Для создания КРГ в ручном режиме нужно деактивировать расписание',
            });
          } else {
            await dispatch(
              WorkGroupCreationPlanStore.actions.updateDataThunk({
                url: apiUrls.workGroupControl.creationPlan.createCabinet,
                body: filteredCheckedRows.map((item) => ({
                  ...item.rowId,
                  // Заменяем пустую дату на null, чтобы избежать ошибки парсинга на сервере
                  date: item.rowId?.date === '' ? null : item.rowId?.date,
                })),
              }),
            ).unwrap();

            notification.success({
              message: 'Кабинет успешно создан',
            });
            getTableWithFilters();
            refetch();
            getBadgeCount();
          }
        },
      },
      {
        title: 'Отмена',
        key: 'cancel',
        loading: plan.buttons.isPending,
        ghost: true,
        danger: true,
        onClick: () => {
          handleResetSearch();
          handleClose();
        },
      },
    ],
    [
      plan.buttons.isPending,
      plan.table.rows,
      canEdit,
      isRowsChecked,
      dispatch,
      getTableWithFilters,
      filteredCheckedRows,
      refetch,
      getBadgeCount,
      handleResetSearch,
      handleClose,
    ],
  );

  /* ----------------------------------------------------
   *                      Сайды
   ---------------------------------------------------- */
  useEffect(() => {
    getTableAndResetFilters();
    return reset;
  }, []); // eslint-disable-line

  return (
    <AppPopup
      isOpened
      onClose={handleClose}
      className={styles.popup}
      title={title}
      additionalFullSizeHandler={toggleFullSize}
      fullSizeClassName={styles.popupFullSize}
    >
      <Divider className={styles.divider}>КРГ доступные для создания</Divider>

      {memoTable}

      <SearchDrawer
        refetchTable={getTableWithFilters}
        handleClose={closeD}
        isOpened={isDrawerOpen}
      />
      <div className={styles.buttonsGroup}>
        <ButtonsContainer buttons={actionButtons} />
        <ButtonsContainer
          buttons={[
            {
              disabled: plan.buttons.isPending,
              title: 'Настроить фильтры',
              type: 'primary',
              key: '1',
              onClick: openD,
            },
            {
              disabled: plan.buttons.isPending,
              title: 'Выгрузить в xlsx',
              key: 'xlsx',
              onClick: () => {
                asyncDownloadFile(downloadReport());
              },
            },
          ]}
        />
      </div>
    </AppPopup>
  );
};
