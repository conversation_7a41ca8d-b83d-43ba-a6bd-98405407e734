import { DatePicker, Typography } from 'antd';
import moment from 'moment';
import type { FC } from 'react';
import { WorkGroupCreationPlanStore } from 'widgets/WorkGroupCreationPlan';
import { TableRowData } from 'features/DataGrid';
import { permissionsStore } from 'entities/Permissions';
import { DEFAULT_DATE_TIME_VARIANT } from 'shared/config/constants';
import { useAppSelector, useCreateSliceActions } from 'shared/model';

import styles from './styles.module.scss';

interface SchedulerProps {
  isFullSize: boolean;
  rows: TableRowData[];
}

export const Scheduler: FC<SchedulerProps> = ({ isFullSize, rows }) => {
  const { handleChangeScheduler } = useCreateSliceActions(
    WorkGroupCreationPlanStore.reducers.slice.actions,
  );
  const plan = useAppSelector(
    WorkGroupCreationPlanStore.selectors.planSelector,
  );

  const permissions = useAppSelector(
    permissionsStore.selectors.permissionsSelector,
  );

  return (
    <div className={styles.scheduler}>
      <Typography.Text>Общие настройки расписания</Typography.Text>
      <DatePicker
        value={
          plan.schedulerValue === null
            ? plan.schedulerValue
            : moment(plan.schedulerValue, DEFAULT_DATE_TIME_VARIANT)
        }
        size={isFullSize ? 'middle' : 'small'}
        disabled={
          !permissions.ap_PlanWGO.includes(
            permissionsStore.enums.Actions.EDIT_LIST_AT,
          ) || rows.every((row) => !row.rowId?.checkedRows)
        }
        showTime
        format={DEFAULT_DATE_TIME_VARIANT}
        placeholder={
          rows.some((row) => row.rowId?.checkedRows)
            ? 'Изменение сроков создания КРГ'
            : 'Выберите КРГ в таблице'
        }
        className={styles.schedulerPicker}
        onChange={(_, dateString) => handleChangeScheduler(dateString || '')}
      />
    </div>
  );
};
